version: "3.8"
services:
  cockroach:
    image: cockroachdb/cockroach:v24.2.0
    command: start-single-node --insecure --http-addr 0.0.0.0:8080
    ports: ["26257:26257","8080:8080"]
    volumes: ["crdb:/cockroach/cockroach-data"]

  nakama:
    image: registry.heroiclabs.com/heroiclabs/nakama:3.22.0
    depends_on: [cockroach]
    volumes:
      - ../server/nakama/nakama.yml:/nakama/data/nakama.yml:ro
      - ../server/nakama/src:/nakama/data/modules
    command: >
      /bin/sh -lc "nakama migrate up --database.address root@cockroach:26257 &&
                   nakama --name nakama1 --database.address root@cockroach:26257
                   --runtime.path /nakama/data/modules --config /nakama/data/nakama.yml"
    ports: ["7350:7350","7349:7349"] # http, grpc

volumes: { crdb: {} }
